import gspread
import google.generativeai as genai
import json
import re # 用于正则表达式解析

# --- 配置信息 ---
# 您的 Google Sheet ID，从浏览器地址栏复制而来
GOOGLE_SHEET_ID = '1Ci7DQiZZvF4aQRxykD-MTY7VbpZkg4zSBKJi1TqhOxY'
# 您的 Google 服务账户密钥文件路径
SERVICE_ACCOUNT_FILE = './project_manager_ai/credentials.json'
# 您的 Google Gemini API Key
GEMINI_API_KEY = 'AIzaSyA5eTHjdArUGlw6RgDoAWU8TZwU0LMk_8U'

# 配置 Gemini API
genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20') # 或者 'gemini-1.5-pro' 如果您有权限

# --- 1. 连接 Google Sheet ---
def connect_to_sheet(user_agent=None):
    try:
        # 使用服务账户进行认证
        gc = gspread.service_account(filename=SERVICE_ACCOUNT_FILE)

        # 设置自定义User-Agent (在新版本的gspread中)
        if user_agent:
            # 在新版本的gspread中，需要通过http_client来设置headers
            if hasattr(gc, 'http_client') and hasattr(gc.http_client, 'session'):
                gc.http_client.session.headers.update({'User-Agent': user_agent})
                print(f"使用自定义User-Agent: {user_agent}")
            else:
                print("当前gspread版本不支持自定义User-Agent设置")

        spreadsheet = gc.open_by_key(GOOGLE_SHEET_ID)
        print("成功连接到 Google Sheet！")
        return spreadsheet
    except Exception as e:
        print(f"连接 Google Sheet 失败: {e}")
        print("请检查您的 Sheet ID、credentials.json 文件路径和权限。")
        return None

# --- 2. 从 Google Sheet 获取数据 ---
def get_tasks_data(spreadsheet):
    try:
        # 默认获取第一个工作表（通常是 'Sheet1' 或您自定义的名称）
        # 如果您的任务在其他工作表，请修改 'Tasks'
        tasks_sheet = spreadsheet.worksheet('Sheet1') # 或者您的工作表名称，如 'Tasks'
        # 获取所有记录，以字典列表形式返回（键是列头）
        data = tasks_sheet.get_all_records()
        print(f"获取到 {len(data)} 条任务数据。")
        return data
    except Exception as e:
        print(f"获取任务数据失败: {e}")
        return []

# --- 3. 更新 Google Sheet 数据 ---
def update_sheet_cell(spreadsheet, sheet_name, task_name, column_name, new_value):
    try:
        sheet = spreadsheet.worksheet(sheet_name)
        # 获取所有列头，找到对应列的索引 (从 1 开始)
        headers = sheet.row_values(1)
        if column_name not in headers:
            print(f"错误：列 '{column_name}' 不存在。")
            return False
        col_index = headers.index(column_name) + 1 # gspread 列索引从 1 开始

        # 查找任务名称对应的行
        # 先假设任务名称在第一列，如果不在请调整
        task_names = sheet.col_values(1) # 获取第一列所有值
        try:
            row_index = task_names.index(task_name) + 1 # gspread 行索引从 1 开始
        except ValueError:
            print(f"错误：任务 '{task_name}' 不存在。")
            return False

        # 更新单元格
        sheet.update_cell(row_index, col_index, new_value)
        print(f"已更新任务 '{task_name}' 的 '{column_name}' 为 '{new_value}'。")
        return True
    except Exception as e:
        print(f"更新单元格失败: {e}")
        return False

# --- 4. 添加新行到 Google Sheet ---
def add_new_task(spreadsheet, sheet_name, task_data):
    try:
        sheet = spreadsheet.worksheet(sheet_name)
        headers = sheet.row_values(1) # 获取列头
        # 根据列头顺序准备要添加的数据列表
        row_to_add = [task_data.get(header, '') for header in headers] # 默认值为空字符串
        sheet.append_row(row_to_add)
        print(f"已添加新任务: '{task_data.get('任务名称', '未知任务')}'。")
        return True
    except Exception as e:
        print(f"添加新任务失败: {e}")
        return False

# --- 5. 与 LLM 交互 (核心部分) ---
def query_llm(user_command, current_sheet_data):
    # 将当前的 Google Sheet 数据转换为 LLM 容易理解的格式
    # 我们可以用 JSON 字符串传递数据
    sheet_data_json = json.dumps(current_sheet_data, ensure_ascii=False, indent=2)

    # 设计给 LLM 的提示 (Prompt)
    # 这部分非常关键，告诉 LLM 它的角色、数据结构以及它应该如何响应
    prompt = f"""
    你是一个智能项目管理助手，能够理解用户的自然语言指令，并针对 Google Sheet 中的项目任务进行操作或查询。
    你的数据源是以下 JSON 格式的项目任务列表：
    ```json
    {sheet_data_json}
    ```

    Google Sheet 的列头是：`任务名称`, `负责人`, `截止日期`, `状态`, `优先级`, `备注`。

    当用户要求你执行以下操作时，请以 **JSON 格式** 返回，以便程序解析。
    不要返回任何其他文字，只返回 JSON！

    **可执行的操作及 JSON 格式：**

    1.  **更新任务信息 (update_task):**
        * 用户指令示例: "把 '编写产品需求' 的状态改成 '已完成'"
        * JSON 格式: `{{ "action": "update_task", "task_name": "任务名称", "column": "列头", "new_value": "新值" }}`
        * 注意：`task_name` 必须是已存在的任务名称，`column` 必须是有效的列头。

    2.  **添加新任务 (add_task):**
        * 用户指令示例: "新增一个任务 '市场分析报告'，负责人是小李，截止日期是 2025-07-01，状态是未开始，优先级是高"
        * JSON 格式: `{{ "action": "add_task", "task_data": {{ "任务名称": "任务名称", "负责人": "负责人", "截止日期": "YYYY-MM-DD", "状态": "状态", "优先级": "优先级", "备注": "备注" }} }}`
        * 注意：`任务名称` 是必填项，其他字段如果用户未提及，可以为空字符串或默认值。

    当用户的问题是查询信息，而不是执行操作时，请直接用清晰简洁的语言回答，不要返回 JSON 格式。

    **用户指令：** {user_command}
    """

    try:
        # 发送请求给 Gemini
        response = model.generate_content(prompt)
        llm_text_response = response.text
        print("\nLLM 原始响应:")
        print(llm_text_response)

        # 尝试解析 JSON
        # LLM 有时会在 JSON 前后添加额外的文本或markdown```json```
        # 我们用正则表达式来提取最可能的 JSON 部分
        json_match = re.search(r'\{.*\}', llm_text_response, re.DOTALL)
        if json_match:
            json_string = json_match.group(0)
            try:
                parsed_response = json.loads(json_string)
                return parsed_response
            except json.JSONDecodeError:
                # 如果不是有效的 JSON，就当作普通文本返回
                print("LLM 响应不是有效的 JSON，按文本处理。")
                return {"action": "answer_question", "answer": llm_text_response}
        else:
            # 如果没有找到 JSON，则返回原始文本作为答案
            return {"action": "answer_question", "answer": llm_text_response}

    except Exception as e:
        print(f"LLM 查询失败: {e}")
        return {"action": "answer_question", "answer": f"抱歉，AI 查询时出现错误: {e}"}

# --- 6. 处理 LLM 返回的指令并执行 ---
def process_llm_response(spreadsheet, llm_output):
    action = llm_output.get('action')

    if action == "update_task":
        task_name = llm_output.get('task_name')
        column = llm_output.get('column')
        new_value = llm_output.get('new_value')
        if task_name and column and new_value:
            update_sheet_cell(spreadsheet, 'Sheet1', task_name, column, new_value)
        else:
            print("更新任务指令缺少参数。")
    elif action == "add_task":
        task_data = llm_output.get('task_data', {})
        if task_data.get('任务名称'):
            add_new_task(spreadsheet, 'Sheet1', task_data)
        else:
            print("添加任务指令缺少任务名称。")
    elif action == "answer_question":
        print("\nAI 助手回复:")
        print(llm_output.get('answer'))
    else:
        print(f"LLM 返回了未知的操作或格式错误: {llm_output}")

# --- 主程序逻辑 ---
def main():
    # 常见User-Agent示例
    user_agents = {
        '1': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        '2': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
        '3': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        '4': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
    }
    
    print("\nUser-Agent Switcher")
    print("1. Chrome (Windows)")
    print("2. Safari (iPhone)")
    print("3. Chrome (Mac)")
    print("4. Firefox (Windows)")
    print("5. 默认User-Agent")
    
    choice = input("请选择User-Agent (1-5): ")
    user_agent = user_agents.get(choice) if choice in user_agents else None
    
    spreadsheet = connect_to_sheet(user_agent)
    if not spreadsheet:
        return

    while True:
        user_command = input("\n请输入您的项目管理指令 (输入 '退出' 结束): ")
        if user_command.lower() == '退出':
            print("程序已退出。")
            break

        # 获取最新的 Google Sheet 数据，作为 LLM 的上下文
        current_tasks = get_tasks_data(spreadsheet)
        if not current_tasks:
            print("无法获取项目数据，请检查表格是否为空或连接是否有问题。")
            continue

        # 发送用户指令和当前数据给 LLM
        llm_output = query_llm(user_command, current_tasks)

        # 根据 LLM 的输出执行相应的 Google Sheet 操作或显示答案
        process_llm_response(spreadsheet, llm_output)

if __name__ == "__main__":
    main()
